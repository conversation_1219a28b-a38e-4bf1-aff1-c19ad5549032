<template>
    <view>
        <u-popup v-model="showPicker" mode="bottom" border-radius="14" :closeable="true" @close="canel">
            <scroll-view scroll-y="true" class="scroll-view">
                <u-form :model="form" ref="form">
                    <!-- 饲养管理筛选项 -->
                    <template v-if="filterType === 'breeding'">
                        <view class="regulatory-area">
                            <h3 class="regulatory-area-title">投喂圈舍：</h3>
                            <u-form-item prop="feedingArea">
                                <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"
                                    v-model="form.feedingArea" placeholder="请输入养殖场名称，10个字以内" />
                            </u-form-item>
                        </view>
                        <view class="regulatory-area">
                            <h3 class="regulatory-area-title">饲料种类：</h3>
                            <u-form-item prop="feedType">
                                <view class="select-input" @click="showFeedTypePicker = true">
                                    <text :style="form.feedType ? 'color:#222' : 'color:#999'">
                                        {{ form.feedType || '请选择' }}
                                    </text>
                                    <text class="arrow">></text>
                                </view>
                            </u-form-item>
                        </view>
                        <view class="regulatory-area">
                            <h3 class="regulatory-area-title">投喂时间：</h3>
                            <view class="time-view">
                                <view class="start-time"
                                    :style="startTime.title === '开始时间' ? 'color:#999' : 'color:#222'"
                                    @click="handleShowTime('startTime')">
                                    {{ startTime.title }}
                                </view>
                                -
                                <view class="start-time" :style="endTime.title == '结束时间' ? 'color:#999' : 'color:#222'"
                                    @click="handleShowTime('endTime')">
                                    {{ endTime.title }}
                                </view>
                            </view>
                        </view>
                    </template>

                    <!-- 生长监测筛选项 -->
                    <template v-if="filterType === 'growth'">
                        <view class="regulatory-area">
                            <h3 class="regulatory-area-title">耳标号：</h3>
                            <u-form-item prop="earTagNo">
                                <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"
                                    v-model="form.earTagNo" placeholder="请输入" />
                            </u-form-item>
                        </view>
                        <view class="regulatory-area">
                            <h3 class="regulatory-area-title">检测时间：</h3>
                            <view class="time-view">
                                <view class="start-time"
                                    :style="startTime.title === '开始时间' ? 'color:#999' : 'color:#222'"
                                    @click="handleShowTime('startTime')">
                                    {{ startTime.title }}
                                </view>
                                -
                                <view class="start-time" :style="endTime.title == '结束时间' ? 'color:#999' : 'color:#222'"
                                    @click="handleShowTime('endTime')">
                                    {{ endTime.title }}
                                </view>
                            </view>
                        </view>
                    </template>

                    <!-- 疾病防控筛选项 -->
                    <template v-if="filterType === 'disease'">
                        <view class="regulatory-area">
                            <h3 class="regulatory-area-title">耳标号：</h3>
                            <u-form-item prop="earTagNo">
                                <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"
                                    v-model="form.earTagNo" placeholder="请输入" />
                            </u-form-item>
                        </view>
                        <view class="regulatory-area">
                            <h3 class="regulatory-area-title">检测时间：</h3>
                            <view class="time-view">
                                <view class="start-time"
                                    :style="startTime.title === '开始时间' ? 'color:#999' : 'color:#222'"
                                    @click="handleShowTime('startTime')">
                                    {{ startTime.title }}
                                </view>
                                -
                                <view class="start-time" :style="endTime.title == '结束时间' ? 'color:#999' : 'color:#222'"
                                    @click="handleShowTime('endTime')">
                                    {{ endTime.title }}
                                </view>
                            </view>
                        </view>
                    </template>

                    <!-- 日常记录筛选项 -->
                    <template v-if="filterType === 'daily'">
                        <view class="regulatory-area">
                            <h3 class="regulatory-area-title">耳标号：</h3>
                            <u-form-item prop="earTagNo">
                                <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"
                                    v-model="form.earTagNo" placeholder="请输入" />
                            </u-form-item>
                        </view>
                        <view class="regulatory-area">
                            <h3 class="regulatory-area-title">记录时间：</h3>
                            <view class="time-view">
                                <view class="start-time"
                                    :style="startTime.title === '开始时间' ? 'color:#999' : 'color:#222'"
                                    @click="handleShowTime('startTime')">
                                    {{ startTime.title }}
                                </view>
                                -
                                <view class="start-time" :style="endTime.title == '结束时间' ? 'color:#999' : 'color:#222'"
                                    @click="handleShowTime('endTime')">
                                    {{ endTime.title }}
                                </view>
                            </view>
                        </view>
                    </template>

                </u-form>
                <view :style="'height:' + (isIphonex ? 48 : 24) + 'rpx'"></view>
            </scroll-view>
            <view class="button-group">
                <view class="button-group-view box">
                    <view class="button-group-reset flex" @click="resetForm">
                        重置
                    </view>
                    <view class="button-group-submit flex" @click="submitForm">
                        确认
                    </view>
                </view>
            </view>
        </u-popup>

        <!-- 饲料种类选择器 -->
        <u-picker v-model="showFeedTypePicker" :range="feedTypeOptions" @confirm="onFeedTypeConfirm"
            @cancel="showFeedTypePicker = false"></u-picker>

        <!-- 日期选择器 -->
        <u-calendar range-bg-color="rgba(64,202,143,0.13)" active-bg-color="#40CA8F" range-color="#40CA8F"
            btn-type="success" v-model="showData" :mode="`range`" @change='changeData'></u-calendar>
    </view>
</template>

<script>
export default {
    props: {
        pickerFilterShow: {
            type: Boolean,
            default: false
        },
        filterType: {
            type: String,
            default: 'breeding', // breeding, growth, disease, daily
            validator: function (value) {
                return ['breeding', 'growth', 'disease', 'daily'].indexOf(value) !== -1
            }
        }
    },
    created() {
    },
    data() {
        return {
            customStyle: { fontSize: '26rpx' },
            placeholderStyle: ';color:#999;font-size: 26rpx;',
            form: {
                // 通用字段
                earTagNo: "", // 耳标号
                startTime: "", // 开始时间
                endTime: "", // 结束时间
                // 日常记录专用字段
                feedingArea: "", // 投喂圈舍
                feedType: "", // 饲料种类
            },
            showPicker: false,
            showData: false,
            showFeedTypePicker: false,
            feedTypeOptions: ['精饲料', '粗饲料', '青饲料', '混合饲料'], // 饲料种类选项
            startTime: {
                title: '开始时间',
                timestamp: 0
            },
            endTime: {
                title: '结束时间',
                timestamp: 0
            },
        }
    },
    watch: {
        pickerFilterShow: {
            handler(newValue) {
                this.showPicker = newValue
            },
            immediate: true,
            deep: true
        }
    },
    computed: {
    },
    methods: {
        handleShowTime() {
            this.showData = true;
        },
        changeData(e) {
            this.startTime.title = e.startDate;
            this.endTime.title = e.endDate;
            this.form.startTime = e.startDate;
            this.form.endTime = e.endDate;
            this.showData = false;
        },
        // 饲料种类选择确认
        onFeedTypeConfirm(value) {
            this.form.feedType = this.feedTypeOptions[value[0]];
            this.showFeedTypePicker = false;
        },
        canel() {
            this.$emit('canel')
        },
        submitForm() {
            // 根据不同的筛选类型，只返回对应的字段
            let filterData = {};

            if (this.filterType === 'breeding') {
                // 饲养管理：投喂圈舍 + 饲料种类 + 投喂时间
               if (this.form.feedingArea) filterData.feedingArea = this.form.feedingArea;
                if (this.form.feedType) filterData.feedType = this.form.feedType;
                if (this.form.startTime) filterData.startTime = this.form.startTime;
                if (this.form.endTime) filterData.endTime = this.form.endTime;
            } else if (this.filterType === 'growth') {
                // 生长监测：耳标号 + 检测时间
                if (this.form.earTagNo) filterData.earTagNo = this.form.earTagNo;
                if (this.form.startTime) filterData.startTime = this.form.startTime;
                if (this.form.endTime) filterData.endTime = this.form.endTime;
            } else if (this.filterType === 'disease') {
                // 疾病防控：耳标号 + 检测时间
                if (this.form.earTagNo) filterData.earTagNo = this.form.earTagNo;
                if (this.form.startTime) filterData.startTime = this.form.startTime;
                if (this.form.endTime) filterData.endTime = this.form.endTime;
            } else if (this.filterType === 'daily') {
                // 日常记录：耳标号 + 记录时间
               if (this.form.earTagNo) filterData.earTagNo = this.form.earTagNo;
                if (this.form.startTime) filterData.startTime = this.form.startTime;
                if (this.form.endTime) filterData.endTime = this.form.endTime;
            }

            console.log('筛选数据:', filterData);
            this.$emit('submitForm', filterData);
        },
        reset() {
            // 重置时间显示
            this.startTime.title = "开始时间";
            this.startTime.timestamp = 0;
            this.endTime.title = "结束时间";
            this.endTime.timestamp = 0;

            // 重置表单数据
            this.form = {
                earTagNo: "",
                startTime: "",
                endTime: "",
                feedingArea: "",
                feedType: "",
            };
        },
        resetForm() {
            this.reset()
            this.$emit('resetSearch', false)
            this.$emit('canel')
        }
    }
}
</script>

<style lang="less" scoped>
.button-group {
    width: 100%;
    padding: 29rpx;
    position: relative;
    background-color: white;
    z-index: 10;
    // margin-top: 53rpx;

    .button-group-view {
        border-radius: 100rpx 100rpx 100rpx 100rpx;
        display: flex;
        background-color: #40CA8F;

        .button-group-reset {
            padding: 20rpx 0;
            text-align: center;
            background: #FFFFFF;
            border-radius: 100rpx 0rpx 120rpx 100rpx;
            border: 2rpx solid #40CA8F;
            font-size: 32rpx;
            color: #40CA8F;
            font-family: PingFang SC-Bold, PingFang SC;
        }

        .button-group-submit {
            padding: 20rpx 0;
            text-align: center;
            background: #40CA8F;
            font-size: 32rpx;
            border-radius: 0rpx 100rpx 100rpx 0rpx;
            color: #FFFFFF;
        }
    }

}

.regulatory-area {
    padding: 29rpx;

    .regulatory-area-title {
        font-size: 28rpx;
        color: #333;
        font-weight: 400;
        padding-bottom: 24rpx;
        font-family: PingFang SC-Bold, PingFang SC;
    }

    .time-view {
        display: flex;
        justify-content: space-between;
        line-height: 50rpx;

        .start-time {
            width: 50%;
            border-bottom: 1px solid #F4F4F4;
            text-align: center;
            color: #999;
        }
    }

    .select-input {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20rpx 0;
        border-bottom: 1px solid #F4F4F4;
        font-size: 26rpx;

        .arrow {
            color: #999;
            font-size: 24rpx;
        }
    }
}

.scroll-view {
    height: 1000rpx;
    overflow-y: scroll;
}

.job-other-type {
    padding: 8rpx 29rpx;
    // padding-top: 0;

    .job-type-title {
        font-size: 28rpx;
        color: #333;
        font-weight: 400;
        padding-bottom: 24rpx;
        font-family: PingFang SC-Bold, PingFang SC;
    }

    .job-type-content {
        display: flex;
        flex-wrap: wrap;

        p {
            color: #999;
            background-color: #F4F4F4;
            padding: 17rpx 47rpx;
            border-radius: 100rpx 100rpx 100rpx 100rpx;
            margin: 0 21rpx 30rpx 0;
            border: 2rpx solid transparent;
        }

        .policy-type-active {
            border: 2rpx solid #40CA8F;
            color: #40CA8F;
            background-color: white;
        }

        .ear-type-active {
            border: 2rpx solid #40CA8F;
            color: #40CA8F;
            background-color: white;
        }
    }
}

.job-type {
    padding: 29rpx;
    margin-top: 10rpx;

    .job-type-title {
        font-size: 28rpx;
        color: #333;
        font-weight: 400;
        padding-bottom: 24rpx;
        font-family: PingFang SC-Bold, PingFang SC;
    }

    .job-type-content {
        display: flex;
        flex-wrap: wrap;

        p {
            color: #999;
            background-color: #F4F4F4;
            padding: 17rpx 47rpx;
            border-radius: 100rpx 100rpx 100rpx 100rpx;
            margin: 0 30rpx 30rpx 0;
            border: 2rpx solid transparent;
        }

        .job-type-active {
            border: 2rpx solid #40CA8F;
            color: #40CA8F;
            background-color: white;
        }

        .policy-type-active {
            border: 2rpx solid #40CA8F;
            color: #40CA8F;
            background-color: white;
        }

        .ear-type-active {
            border: 2rpx solid #40CA8F;
            color: #40CA8F;
            background-color: white;
        }
    }

}

uni-picker-view {
    display: block;
}

uni-picker-view .uni-picker-view-wrapper {
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    height: 100%;
    background-color: white;
}

uni-picker-view[hidden] {
    display: none;
}

picker-view {
    width: 100%;
    // height: 600upx;
    height: 600rpx;
    margin-top: 20upx;
}

.item {
    line-height: 100upx;
    text-align: center;
}

.popup-view {
    .picker-btn {
        display: flex;
        justify-content: space-between;
        padding: 30rpx 40rpx;

        .left {
            color: #999;
            font-size: 28rpx;
            font-family: PingFang SC-Medium;
        }

        .middle {
            font-size: 32rpx;
            color: #222;
            font-family: PingFang SC-Heavy;
        }

        .right {
            color: #40CA8F;
            font-size: 32rpx;
            font-family: PingFang SC-Medium;
        }
    }
}
</style>