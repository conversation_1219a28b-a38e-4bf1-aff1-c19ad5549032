<template>
	<view class="container">
		<view class="header">
			<text class="title">筛选组件测试</text>
		</view>
		
		<!-- Tab 切换 -->
		<view class="tab-container">
			<view 
				class="tab-item" 
				:class="{ active: currentTab === 'breeding' }"
				@click="switchTab('breeding')"
			>
				饲养管理
			</view>
			<view 
				class="tab-item" 
				:class="{ active: currentTab === 'growth' }"
				@click="switchTab('growth')"
			>
				生长监测
			</view>
			<view 
				class="tab-item" 
				:class="{ active: currentTab === 'disease' }"
				@click="switchTab('disease')"
			>
				疾病防控
			</view>
			<view 
				class="tab-item" 
				:class="{ active: currentTab === 'daily' }"
				@click="switchTab('daily')"
			>
				日常记录
			</view>
		</view>

		<!-- 当前筛选条件显示 -->
		<view class="current-filter" v-if="Object.keys(filterParams).length > 0">
			<text class="filter-title">当前筛选条件：</text>
			<view class="filter-content">
				{{ JSON.stringify(filterParams, null, 2) }}
			</view>
		</view>

		<!-- 筛选按钮 -->
		<view class="filter-btn" @click="showFilter = true">
			<text>🔍 筛选</text>
		</view>

		<!-- 模拟数据展示 -->
		<view class="data-section">
			<text class="section-title">{{ getTabName(currentTab) }}数据</text>
			<view class="data-item" v-for="(item, index) in mockData" :key="index">
				<text>{{ item }}</text>
			</view>
		</view>

		<!-- 筛选弹窗 -->
		<filter-popup 
			:pickerFilterShow="showFilter" 
			:filterType="currentTab"
			@canel="showFilter = false"
			@submitForm="handleFilter"
			@resetSearch="handleReset"
		></filter-popup>
	</view>
</template>

<script>
import FilterPopup from '@/components/filterPopup/index.vue'

export default {
	components: {
		FilterPopup
	},
	data() {
		return {
			currentTab: 'breeding', // 当前选中的tab
			showFilter: false, // 是否显示筛选弹窗
			filterParams: {}, // 当前筛选参数
			mockData: [
				'数据项 1',
				'数据项 2', 
				'数据项 3'
			]
		}
	},
	methods: {
		// 切换tab
		switchTab(tab) {
			this.currentTab = tab;
			this.filterParams = {}; // 切换tab时清空筛选条件
			console.log('切换到:', this.getTabName(tab));
		},
		
		// 获取tab名称
		getTabName(tab) {
			const names = {
				breeding: '饲养管理',
				growth: '生长监测', 
				disease: '疾病防控',
				daily: '日常记录'
			};
			return names[tab] || tab;
		},
		
		// 处理筛选
		handleFilter(filterData) {
			this.filterParams = filterData;
			this.showFilter = false;
			
			console.log('筛选类型:', this.currentTab);
			console.log('筛选数据:', filterData);
			
			uni.showToast({
				title: '筛选成功',
				icon: 'success'
			});
		},
		
		// 重置筛选
		handleReset() {
			this.filterParams = {};
			console.log('重置筛选');
			
			uni.showToast({
				title: '已重置',
				icon: 'success'
			});
		}
	}
}
</script>

<style lang="less" scoped>
.container {
	padding: 20rpx;
	background: #f5f5f5;
	min-height: 100vh;
}

.header {
	text-align: center;
	padding: 40rpx 0;
	
	.title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}
}

.tab-container {
	display: flex;
	background: white;
	border-radius: 20rpx;
	padding: 10rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
	
	.tab-item {
		flex: 1;
		text-align: center;
		padding: 25rpx 10rpx;
		border-radius: 15rpx;
		font-size: 26rpx;
		color: #666;
		transition: all 0.3s;
		
		&.active {
			background: #40CA8F;
			color: white;
			font-weight: bold;
		}
	}
}

.current-filter {
	background: white;
	padding: 30rpx;
	border-radius: 20rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
	
	.filter-title {
		font-size: 28rpx;
		color: #333;
		font-weight: bold;
		margin-bottom: 20rpx;
	}
	
	.filter-content {
		background: #f8f8f8;
		padding: 20rpx;
		border-radius: 10rpx;
		font-size: 24rpx;
		color: #666;
		white-space: pre-wrap;
	}
}

.filter-btn {
	background: linear-gradient(135deg, #40CA8F, #36B37E);
	color: white;
	padding: 30rpx;
	border-radius: 20rpx;
	text-align: center;
	margin-bottom: 30rpx;
	font-size: 32rpx;
	font-weight: bold;
	box-shadow: 0 8rpx 25rpx rgba(64, 202, 143, 0.3);
	
	&:active {
		transform: translateY(2rpx);
	}
}

.data-section {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
	
	.section-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 30rpx;
		display: block;
	}
	
	.data-item {
		padding: 25rpx;
		background: #f8f8f8;
		margin-bottom: 20rpx;
		border-radius: 15rpx;
		font-size: 28rpx;
		color: #666;
		
		&:last-child {
			margin-bottom: 0;
		}
	}
}
</style>
